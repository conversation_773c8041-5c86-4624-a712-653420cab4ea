'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Maximize, Search, Star, Users, Gamepad2, TrendingUp } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';

// 导入游戏数据
import { gamesData, GameCardProps } from '@/data/games';
import OptimizedImage, { imageSizes } from '@/components/OptimizedImage';
import { useMobileDetection, mobileClasses } from '@/components/MobileDetection';
import MobileNavigation from '@/components/MobileNavigation';

// 游戏分类数据
const categories = [
  { id: 'action', name: { en: 'Action', zh: '动作' }, icon: '🎮' },
  { id: 'adventure', name: { en: 'Adventure', zh: '冒险' }, icon: '🗺️' },
  { id: 'racing', name: { en: 'Racing', zh: '竞速' }, icon: '🏎️' },
  { id: 'shooting', name: { en: 'Shooting', zh: '射击' }, icon: '🔫' },
  { id: 'puzzle', name: { en: 'Puzzle', zh: '益智' }, icon: '🧩' },
  { id: 'strategy', name: { en: 'Strategy', zh: '策略' }, icon: '⚔️' },
  { id: 'sports', name: { en: 'Sports', zh: '体育' }, icon: '⚽' },
  { id: 'simulation', name: { en: 'Simulation', zh: '模拟' }, icon: '🎯' }
];

export default function Home() {
  const [selectedCategory, setSelectedCategory] = useState<string>('action');
  const [selectedGame, setSelectedGame] = useState<GameCardProps | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const t = useTranslations('Home');
  const locale = useLocale();
  const { isMobile, isTablet, deviceType } = useMobileDetection();
  
  // 提前计算当前选中的分类名称
  const currentCategoryName = React.useMemo(() => {
    const category = categories.find(c => c.id === selectedCategory);
    return category ? (category.name[locale as keyof typeof category.name] || category.name['en']) : '';
  }, [selectedCategory, locale]);
  
  const handleFullscreen = () => {
    const iframe = document.getElementById('game-iframe') as HTMLIFrameElement;
    if (iframe) {
      if (iframe.requestFullscreen) {
        iframe.requestFullscreen();
      }
    }
  };

  return (
    <div className="space-y-8">
      {/* Hero Section with Main H1 - Mobile Optimized */}
      <section className={`${isMobile ? 'py-8' : 'py-12'} text-center`}>
        <div className={`container mx-auto ${mobileClasses.mobileSpacing}`}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className={`${
              isMobile
                ? 'text-2xl sm:text-3xl'
                : 'text-3xl md:text-5xl lg:text-6xl'
            } font-bold mb-4 font-display bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent`}>
              {t('title')}
            </h1>
            <p className={`${
              isMobile
                ? 'text-base px-2'
                : 'text-lg md:text-xl'
            } text-muted-foreground mb-6 max-w-3xl mx-auto`}>
              {t('description')}
            </p>
            
            {/* Search Bar */}
            <div className="max-w-md mx-auto mb-8">
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  if (searchQuery.trim()) {
                    window.location.href = `/${locale}/search?q=${encodeURIComponent(searchQuery.trim())}`;
                  }
                }}
                className="relative"
              >
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                <input
                  type="text"
                  placeholder={t('searchPlaceholder') || 'Search games...'}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-full border border-border bg-background/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all"
                />
                {searchQuery && (
                  <button
                    type="submit"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 px-3 py-1 text-sm bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-colors"
                  >
                    Search
                  </button>
                )}
              </form>
            </div>

            {/* Stats Section */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Gamepad2 className="h-6 w-6 text-primary mr-2" />
                  <span className="text-2xl font-bold text-primary">4,500+</span>
                </div>
                <p className="text-sm text-muted-foreground">{t('totalGames') || 'Free Games'}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-6 w-6 text-primary mr-2" />
                  <span className="text-2xl font-bold text-primary">30M+</span>
                </div>
                <p className="text-sm text-muted-foreground">{t('monthlyPlayers') || 'Monthly Players'}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Star className="h-6 w-6 text-primary mr-2" />
                  <span className="text-2xl font-bold text-primary">4.8</span>
                </div>
                <p className="text-sm text-muted-foreground">{t('averageRating') || 'Average Rating'}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="h-6 w-6 text-primary mr-2" />
                  <span className="text-2xl font-bold text-primary">100%</span>
                </div>
                <p className="text-sm text-muted-foreground">{t('freeToPlay') || 'Free to Play'}</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Game Categories Section */}
      <section className="py-8 bg-secondary/50 rounded-xl">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="flex items-center justify-between mb-6"
          >
            <h2 className="text-2xl md:text-3xl font-bold">{t('exploreGames')}</h2>
            <Link
              href={`/${locale}/games/${selectedCategory}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:text-primary/80 transition-colors font-medium"
            >
              {t('viewAll')}
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex flex-wrap gap-3"
          >
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full font-medium transition-all ${
                  selectedCategory === category.id
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                }`}
              >
                <span className="mr-2">{category.icon}</span>
                {category.name[locale as keyof typeof category.name] || category.name['en']}
              </button>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Featured Games Section */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          {selectedGame ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl md:text-3xl font-bold">{selectedGame.title[locale]}</h2>
                <button
                  onClick={() => setSelectedGame(null)}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('backToCategories')}
                </button>
              </div>

              <div className="relative bg-card rounded-lg overflow-hidden shadow-lg">
                <div className="aspect-video w-full relative">
                  <iframe
                    id="game-iframe"
                    src={selectedGame.gameUrl}
                    className="absolute w-full h-full border-none"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                    allowFullScreen
                    title={selectedGame.title[locale as keyof typeof selectedGame.title] || selectedGame.title['en']}
                  ></iframe>

                  <div className="absolute bottom-4 right-4 flex space-x-2">
                    <button
                      onClick={handleFullscreen}
                      className="p-2 bg-black/70 text-white rounded-full hover:bg-black/90 transition-all"
                      title={t('fullscreen')}
                    >
                      <Maximize className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <h2 className="text-2xl md:text-3xl font-bold">
                {t('featuredGames')} - {currentCategoryName}
              </h2>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {gamesData[selectedCategory]?.map((game) => (
                  <Link
                    key={game.id}
                    href={`/${locale}/game/${game.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className="group cursor-pointer"
                    >
                      <div className="aspect-video rounded-lg overflow-hidden bg-secondary">
                        <OptimizedImage
                          src={game.image}
                          alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                          className="w-full h-full transition-transform duration-300 group-hover:scale-105"
                          width={320}
                          height={180}
                          fill={true}
                          sizes={imageSizes.gameCard}
                          placeholder="blur"
                        />
                      </div>
                      <div className="mt-2">
                        <h3 className="font-medium text-lg">{game.title[locale as keyof typeof game.title] || game.title['en']}</h3>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <span>{game.category[locale as keyof typeof game.category] || game.category['en']}</span>
                          <span className="mx-2">•</span>
                          <span>⭐ {game.rating}</span>
                        </div>
                      </div>
                    </motion.div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-12 bg-secondary/50 rounded-xl">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl md:text-4xl font-bold mb-4 font-display">{t('readyToPlay')}</h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            {t('discoverMore')}
          </p>
          <Link
            href={`/${locale}/games/${selectedCategory}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <button className="px-8 py-3 bg-primary text-primary-foreground rounded-full font-medium shadow-lg shadow-primary/20 hover:bg-primary/90 transition-all">
              {t('browseAllGames')}
            </button>
          </Link>
        </div>
      </section>

      {/* Mobile Navigation */}
      {(isMobile || isTablet) && <MobileNavigation />}
    </div>
  );
}
