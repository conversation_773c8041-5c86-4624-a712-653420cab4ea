'use client';

import React, { useState, useMemo } from 'react';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Tag, Filter, Grid, List } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { gamesData, GameCardProps, getGamesByTag } from '@/data/games';
import { gameDetailsData } from '@/data/game-details';
import { gameTags } from '@/components/GameTags';


export default function TagPage() {
  const params = useParams();
  const locale = useLocale();
  const t = useTranslations('Tags');
  
  const tag = params.tag as string;
  const [sortBy, setSortBy] = useState<'rating' | 'name' | 'newest'>('rating');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 获取标签对应的游戏
  const taggedGames = useMemo(() => {
    const games: GameCardProps[] = [];
    
    // 遍历所有游戏数据
    Object.entries(gamesData).forEach(([category, categoryGames]) => {
      categoryGames.forEach(game => {
        if (game.tags?.includes(tag)) {
          const gameDetail = gameDetailsData[game.id];
          if (gameDetail) {
            games.push({
              ...game,
              title: gameDetail.title,
              category: gameDetail.category
            });
          }
        }
      });
    });

    // 排序
    games.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          const nameA = a.title[locale as keyof typeof a.title] || a.title['en'];
          const nameB = b.title[locale as keyof typeof b.title] || b.title['en'];
          return nameA.localeCompare(nameB);
        case 'newest':
          // 这里可以根据游戏的添加时间排序，暂时使用ID排序
          return b.id.localeCompare(a.id);
        case 'rating':
        default:
          return b.rating - a.rating;
      }
    });

    return games;
  }, [tag, sortBy, locale]);

  // 获取标签显示名称
  const tagDisplayName = gameTags[tag as keyof typeof gameTags]?.[locale as keyof typeof gameTags[keyof typeof gameTags]] || 
                        gameTags[tag as keyof typeof gameTags]?.['en'] || 
                        tag;

  return (
    <div className="min-h-screen bg-background">
      {/* 面包屑导航 */}
      <div className="container mx-auto px-4 py-4">
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Link href={`/${locale}`} className="hover:text-foreground transition-colors">
            {t('home') || 'Home'}
          </Link>
          <span>/</span>
          <Link href={`/${locale}/tags`} className="hover:text-foreground transition-colors">
            {t('tags') || 'Tags'}
          </Link>
          <span>/</span>
          <span className="text-foreground">{tagDisplayName}</span>
        </nav>
      </div>

      {/* 标签头部 */}
      <div className="bg-secondary/30 py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex items-center justify-center mb-4">
              <Tag className="h-8 w-8 text-primary mr-3" />
              <h1 className="text-3xl md:text-4xl font-bold">
                {tagDisplayName}
              </h1>
            </div>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              {t('tagDescription', { tag: tagDisplayName, count: taggedGames.length }) || 
               `Discover ${taggedGames.length} games tagged with "${tagDisplayName}"`}
            </p>
            
            {/* 游戏数量统计 */}
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full">
              <span className="text-primary font-semibold">
                {taggedGames.length} {t('games') || 'games'}
              </span>
            </div>
          </motion.div>
        </div>
      </div>

      {/* 过滤器和视图控制 */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium">
              {t('showing')} {taggedGames.length} {t('games')}
            </span>
          </div>

          <div className="flex items-center space-x-4">
            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'rating' | 'name' | 'newest')}
              className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="rating">{t('sortRating') || 'Rating'}</option>
              <option value="name">{t('sortName') || 'Name'}</option>
              <option value="newest">{t('sortNewest') || 'Newest'}</option>
            </select>

            {/* 视图模式切换 */}
            <div className="flex items-center border border-border rounded-lg">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-l-lg`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-r-lg`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* 游戏列表 */}
        {taggedGames.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className={
              viewMode === 'grid'
                ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
                : "space-y-4"
            }
          >
            {taggedGames.map((game, index) => (
              <motion.div
                key={game.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                {viewMode === 'grid' ? (
                  <Link
                    href={`/${locale}/game/${game.id}`}
                    className="block group"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <div className="relative aspect-video rounded-lg overflow-hidden bg-secondary">
                      <Image
                        src={game.image}
                        alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        width={320}
                        height={180}
                        loading="lazy"
                      />
                    </div>
                    <div className="mt-2 space-y-1">
                      <h3 className="font-medium text-foreground line-clamp-1">
                        {game.title[locale as keyof typeof game.title] || game.title['en']}
                      </h3>
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>{game.category[locale as keyof typeof game.category] || game.category['en']}</span>
                        <span className="flex items-center">
                          ⭐ {game.rating}
                        </span>
                      </div>
                    </div>
                  </Link>
                ) : (
                  <Link
                    href={`/${locale}/game/${game.id}`}
                    className="flex items-center space-x-4 p-4 rounded-lg border border-border hover:border-primary/50 transition-colors group"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <div className="relative w-24 h-16 rounded-lg overflow-hidden bg-secondary flex-shrink-0">
                      <Image
                        src={game.image}
                        alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        width={96}
                        height={64}
                        loading="lazy"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-foreground line-clamp-1 mb-1">
                        {game.title[locale as keyof typeof game.title] || game.title['en']}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>{game.category[locale as keyof typeof game.category] || game.category['en']}</span>
                        <span className="flex items-center">
                          ⭐ {game.rating}
                        </span>
                        {game.tags && (
                          <div className="flex items-center space-x-1">
                            {game.tags.slice(0, 3).map((tag) => (
                              <span key={tag} className="px-2 py-1 bg-secondary rounded-full text-xs">
                                {gameTags[tag as keyof typeof gameTags]?.[locale as keyof typeof gameTags[keyof typeof gameTags]] || 
                                 gameTags[tag as keyof typeof gameTags]?.['en'] || 
                                 tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                )}
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">🏷️</div>
            <h3 className="text-xl font-semibold mb-2">
              {t('noGamesFound') || 'No games found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('noGamesDescription') || 'No games are currently tagged with this label'}
            </p>
            <Button asChild variant="outline">
              <Link href={`/${locale}`}>
                {t('backToHome') || 'Back to Home'}
              </Link>
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  );
}
