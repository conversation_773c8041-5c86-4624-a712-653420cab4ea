'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Sparkles, Star, Calendar, Clock, Filter, Grid, List, Plus } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { gamesData, GameCardProps } from '@/data/games';
import { gameDetailsData } from '@/data/game-details';
import GameCard from '@/components/GameCard';
import Breadcrumb from '@/components/Breadcrumb';
import { useMobileDetection } from '@/components/MobileDetection';
import MobileNavigation from '@/components/MobileNavigation';
import ResponsiveGrid, { ResponsiveContainer, ResponsiveText } from '@/components/ResponsiveGrid';

export default function NewGamesPage() {
  const locale = useLocale();
  const t = useTranslations('NewGames');
  const { isMobile, isTablet } = useMobileDetection();
  
  const [sortBy, setSortBy] = useState<'newest' | 'rating' | 'category'>('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [timeFilter, setTimeFilter] = useState<'week' | 'month' | 'quarter' | 'all'>('month');

  // 获取新游戏（基于ID和标签）
  const newGames = useMemo(() => {
    const games: (GameCardProps & { addedDate: Date; isNew: boolean; categoryId: string })[] = [];
    
    Object.entries(gamesData).forEach(([category, categoryGames]) => {
      categoryGames.forEach(game => {
        const gameDetail = gameDetailsData[game.id];
        if (gameDetail) {
          // 模拟添加日期（实际应该从数据库获取）
          const addedDate = new Date();
          addedDate.setDate(addedDate.getDate() - Math.floor(Math.random() * 90)); // 随机90天内
          
          // 判断是否为新游戏
          const isNew = game.tags?.includes('new') || 
                       addedDate > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30天内
          
          if (isNew) {
            games.push({
              ...game,
              title: gameDetail.title,
              category: gameDetail.category,
              categoryId: category,
              addedDate,
              isNew
            });
          }
        }
      });
    });

    // 按分类过滤
    let filtered = games;
    if (categoryFilter !== 'all') {
      filtered = games.filter(game => game.categoryId === categoryFilter);
    }

    // 按时间过滤
    const now = new Date();
    const timeFilterDays = {
      week: 7,
      month: 30,
      quarter: 90,
      all: 365
    };
    
    const cutoffDate = new Date(now.getTime() - timeFilterDays[timeFilter] * 24 * 60 * 60 * 1000);
    filtered = filtered.filter(game => game.addedDate >= cutoffDate);

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'category':
          const catA = a.category[locale as keyof typeof a.category] || a.category['en'];
          const catB = b.category[locale as keyof typeof b.category] || b.category['en'];
          return catA.localeCompare(catB);
        case 'newest':
        default:
          return b.addedDate.getTime() - a.addedDate.getTime();
      }
    });

    return filtered;
  }, [sortBy, categoryFilter, timeFilter, locale]);

  // 获取分类列表
  const categories = useMemo(() => {
    const cats = new Set<string>();
    Object.keys(gamesData).forEach(cat => cats.add(cat));
    return Array.from(cats);
  }, []);

  // 统计信息
  const stats = useMemo(() => {
    const thisWeek = newGames.filter(game => 
      game.addedDate > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    ).length;
    
    const thisMonth = newGames.filter(game => 
      game.addedDate > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length;
    
    const avgRating = newGames.length > 0 
      ? newGames.reduce((sum, game) => sum + game.rating, 0) / newGames.length 
      : 0;
    
    return {
      totalNew: newGames.length,
      thisWeek,
      thisMonth,
      avgRating: avgRating.toFixed(1)
    };
  }, [newGames]);

  // 格式化日期
  const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return t('yesterday') || 'Yesterday';
    if (diffDays <= 7) return t('daysAgo', { days: diffDays }) || `${diffDays} days ago`;
    if (diffDays <= 30) return t('weeksAgo', { weeks: Math.ceil(diffDays / 7) }) || `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString(locale);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 面包屑导航 */}
      <ResponsiveContainer padding="md" className="py-4">
        <Breadcrumb items={[{ label: t('newGames') || 'New Games', isCurrentPage: true }]} />
      </ResponsiveContainer>

      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-emerald-500 to-teal-500 py-16">
        <ResponsiveContainer>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center text-white"
          >
            <div className="flex items-center justify-center mb-4">
              <Sparkles className="h-12 w-12 mr-3" />
              <ResponsiveText
                as="h1"
                size="4xl"
                className="font-bold"
              >
                {t('title') || 'New Games'}
              </ResponsiveText>
            </div>
            <ResponsiveText
              size="lg"
              className="opacity-90 max-w-3xl mx-auto mb-8"
            >
              {t('description') || 'Discover the latest games added to our collection. Fresh content updated regularly!'}
            </ResponsiveText>
            
            {/* 统计信息 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.totalNew}</div>
                <div className="text-sm opacity-80">{t('newGames') || 'New Games'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.thisWeek}</div>
                <div className="text-sm opacity-80">{t('thisWeek') || 'This Week'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.thisMonth}</div>
                <div className="text-sm opacity-80">{t('thisMonth') || 'This Month'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.avgRating}</div>
                <div className="text-sm opacity-80">{t('avgRating') || 'Avg Rating'}</div>
              </div>
            </div>
          </motion.div>
        </ResponsiveContainer>
      </div>

      {/* 最新添加的游戏亮点 */}
      <ResponsiveContainer className="py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold mb-6 text-center">
            {t('latestAdditions') || 'Latest Additions'}
          </h2>
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950 dark:to-teal-950 rounded-lg p-6">
            <ResponsiveGrid
              mobileColumns={1}
              tabletColumns={2}
              desktopColumns={3}
              className="gap-4"
            >
              {newGames.slice(0, 6).map((game, index) => (
                <Link
                  key={game.id}
                  href={`/${locale}/game/${game.id}`}
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-white/50 dark:hover:bg-black/20 transition-colors group"
                >
                  <div className="relative w-16 h-12 rounded-lg overflow-hidden bg-secondary flex-shrink-0">
                    <img
                      src={game.image}
                      alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-1 right-1">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-foreground line-clamp-1 group-hover:text-primary transition-colors">
                      {game.title[locale as keyof typeof game.title] || game.title['en']}
                    </h3>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        {game.rating}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(game.addedDate)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center text-emerald-600">
                    <Plus className="h-4 w-4" />
                  </div>
                </Link>
              ))}
            </ResponsiveGrid>
          </div>
        </motion.div>
      </ResponsiveContainer>

      {/* 过滤器和视图控制 */}
      <ResponsiveContainer className="py-6">
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium">
              {t('showing')} {newGames.length} {t('newGames')}
            </span>
          </div>

          <div className="flex flex-wrap items-center gap-4">
            {/* 时间过滤器 */}
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value as 'week' | 'month' | 'quarter' | 'all')}
              className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="week">{t('lastWeek') || 'Last Week'}</option>
              <option value="month">{t('lastMonth') || 'Last Month'}</option>
              <option value="quarter">{t('last3Months') || 'Last 3 Months'}</option>
              <option value="all">{t('allTime') || 'All Time'}</option>
            </select>

            {/* 分类过滤器 */}
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="all">{t('allCategories') || 'All Categories'}</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>

            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'newest' | 'rating' | 'category')}
              className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="newest">{t('sortNewest') || 'Newest First'}</option>
              <option value="rating">{t('sortRating') || 'Highest Rated'}</option>
              <option value="category">{t('sortCategory') || 'Category'}</option>
            </select>

            {/* 视图模式切换 */}
            {!isMobile && (
              <div className="flex items-center border border-border rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-l-lg`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-r-lg`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 游戏列表 */}
        {newGames.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {viewMode === 'grid' || isMobile ? (
              <ResponsiveGrid
                mobileColumns={1}
                tabletColumns={2}
                desktopColumns={4}
                className="gap-6"
              >
                {newGames.map((game, index) => (
                  <motion.div
                    key={game.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.02 }}
                    className="relative"
                  >
                    {/* 新游戏标识 */}
                    <div className="absolute top-2 left-2 z-10 px-2 py-1 bg-emerald-500 text-white rounded-full text-xs font-medium">
                      ✨ {t('new') || 'NEW'}
                    </div>
                    <GameCard game={game} locale={locale} priority={index < 8} />
                    {/* 添加日期 */}
                    <div className="mt-2 text-xs text-muted-foreground text-center">
                      {t('added')} {formatDate(game.addedDate)}
                    </div>
                  </motion.div>
                ))}
              </ResponsiveGrid>
            ) : (
              <div className="space-y-4">
                {newGames.map((game, index) => (
                  <motion.div
                    key={game.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.02 }}
                    className="flex items-center space-x-4 p-4 rounded-lg border border-border hover:border-primary/50 transition-colors"
                  >
                    <div className="relative w-24 h-16 rounded-lg overflow-hidden bg-secondary flex-shrink-0">
                      <img
                        src={game.image}
                        alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-1 right-1">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-foreground line-clamp-1 mb-1">
                        {game.title[locale as keyof typeof game.title] || game.title['en']}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span className="flex items-center">
                          <Star className="h-3 w-3 mr-1" />
                          {game.rating}
                        </span>
                        <span className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(game.addedDate)}
                        </span>
                        <span>{game.category[locale as keyof typeof game.category] || game.category['en']}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="px-2 py-1 bg-emerald-100 text-emerald-800 rounded-full text-xs mb-2">
                        {t('new') || 'NEW'}
                      </div>
                      <Link
                        href={`/${locale}/game/${game.id}`}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                      >
                        {t('play') || 'Play'}
                      </Link>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">🎮</div>
            <h3 className="text-xl font-semibold mb-2">
              {t('noNewGames') || 'No new games found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('noNewGamesDescription') || 'Try adjusting your time filter or check back later for new additions'}
            </p>
            <button
              onClick={() => {
                setTimeFilter('all');
                setCategoryFilter('all');
              }}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('showAllGames') || 'Show All Games'}
            </button>
          </motion.div>
        )}
      </ResponsiveContainer>

      {/* 移动端导航 */}
      {(isMobile || isTablet) && <MobileNavigation />}
    </div>
  );
}
