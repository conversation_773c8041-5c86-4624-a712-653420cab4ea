import Link from 'next/link';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import type { GameCardProps } from '@/data/games';
import GameTags from './GameTags';
import OptimizedImage, { imageSizes } from './OptimizedImage';

interface Props {
  game: GameCardProps;
  locale: string;
  priority?: boolean;
}

export default function GameCard({ game, locale, priority = false }: Props) {
  const t = useTranslations('Navigation.categoryList');

  return (
    <Link
      href={`/${locale}/game/${game.id}`}
      className="block group"
      aria-label={`${t('play')} ${game.title[locale as keyof typeof game.title] || game.title['en']}`}
      target="_blank"
      rel="noopener noreferrer"
    >
      <div className="relative aspect-video rounded-lg overflow-hidden bg-secondary">
        <OptimizedImage
          src={game.image}
          alt={game.title[locale as keyof typeof game.title] || game.title['en']}
          className="w-full h-full transition-transform duration-300 group-hover:scale-105"
          width={320}
          height={180}
          priority={priority}
          fill={true}
          sizes={imageSizes.gameCard}
          placeholder="blur"
          quality={85}
        />
      </div>
      <div className="mt-2 space-y-2">
        <h3 className="font-medium text-foreground line-clamp-1">
          {game.title[locale as keyof typeof game.title] || game.title['en']}
        </h3>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span className="line-clamp-1">
            {game.category[locale as keyof typeof game.category] || game.category['en']}
          </span>
          <span className="flex items-center">
            ⭐ {game.rating}
          </span>
        </div>
        {game.tags && game.tags.length > 0 && (
          <GameTags tags={game.tags} maxTags={2} showLinks={true} size="sm" />
        )}
      </div>
    </Link>
  );
} 