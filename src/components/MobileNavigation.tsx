'use client';

import { useState, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import { Home, Search, Tag, Menu, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { useTouchGestures, TouchButton } from './TouchGestures';
import { useMobileDetection } from './MobileDetection';

interface MobileNavigationProps {
  className?: string;
}

export default function MobileNavigation({ className = '' }: MobileNavigationProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { isMobile, isTablet } = useMobileDetection();
  const pathname = usePathname();
  const locale = useLocale();
  const t = useTranslations('Navigation');
  const menuRef = useRef<HTMLDivElement>(null);

  // Touch gestures for menu
  useTouchGestures(menuRef, {
    onSwipe: (gesture) => {
      if (gesture.direction === 'left' && isMenuOpen) {
        setIsMenuOpen(false);
      } else if (gesture.direction === 'right' && !isMenuOpen) {
        setIsMenuOpen(true);
      }
    },
  });

  const navigationItems = [
    {
      href: `/${locale}`,
      icon: Home,
      label: t('home') || 'Home',
      isActive: pathname === `/${locale}`,
    },
    {
      href: `/${locale}/search`,
      icon: Search,
      label: t('search') || 'Search',
      isActive: pathname.startsWith(`/${locale}/search`),
    },
    {
      href: `/${locale}/tags`,
      icon: Tag,
      label: t('tags') || 'Tags',
      isActive: pathname.startsWith(`/${locale}/tags`),
    },
  ];

  if (!isMobile && !isTablet) {
    return null; // Don't show on desktop
  }

  return (
    <>
      {/* Mobile Bottom Navigation */}
      <nav className={`fixed bottom-0 left-0 right-0 bg-background border-t border-border z-40 ${className}`}>
        <div className="flex items-center justify-around py-2 px-4">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <TouchButton
                key={item.href}
                onClick={() => {
                  // Navigation will be handled by Link
                }}
                className={`flex flex-col items-center justify-center p-2 rounded-lg transition-colors ${
                  item.isActive
                    ? 'text-primary bg-primary/10'
                    : 'text-muted-foreground hover:text-foreground hover:bg-secondary/50'
                }`}
              >
                <Link href={item.href} className="flex flex-col items-center">
                  <Icon className="h-5 w-5 mb-1" />
                  <span className="text-xs font-medium">{item.label}</span>
                </Link>
              </TouchButton>
            );
          })}
          
          {/* Menu Button */}
          <TouchButton
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className={`flex flex-col items-center justify-center p-2 rounded-lg transition-colors ${
              isMenuOpen
                ? 'text-primary bg-primary/10'
                : 'text-muted-foreground hover:text-foreground hover:bg-secondary/50'
            }`}
          >
            {isMenuOpen ? (
              <X className="h-5 w-5 mb-1" />
            ) : (
              <Menu className="h-5 w-5 mb-1" />
            )}
            <span className="text-xs font-medium">
              {isMenuOpen ? 'Close' : 'Menu'}
            </span>
          </TouchButton>
        </div>
      </nav>

      {/* Slide-out Menu */}
      {isMenuOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 z-50"
            onClick={() => setIsMenuOpen(false)}
          />
          
          {/* Menu Panel */}
          <div
            ref={menuRef}
            className="fixed right-0 top-0 bottom-0 w-80 max-w-[80vw] bg-background border-l border-border z-50 transform transition-transform duration-300 ease-in-out"
          >
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-border">
                <h2 className="text-lg font-semibold">Menu</h2>
                <TouchButton
                  onClick={() => setIsMenuOpen(false)}
                  className="p-2 rounded-full hover:bg-secondary"
                >
                  <X className="h-5 w-5" />
                </TouchButton>
              </div>

              {/* Menu Items */}
              <div className="flex-1 overflow-y-auto py-4">
                <div className="space-y-2 px-4">
                  {/* Game Categories */}
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-muted-foreground mb-3 uppercase tracking-wide">
                      Categories
                    </h3>
                    <div className="space-y-1">
                      {[
                        { name: 'Action', href: `/${locale}/games/action` },
                        { name: 'Adventure', href: `/${locale}/games/adventure` },
                        { name: 'Racing', href: `/${locale}/games/racing` },
                        { name: 'Shooting', href: `/${locale}/games/shooting` },
                        { name: 'Puzzle', href: `/${locale}/games/puzzle` },
                        { name: 'Strategy', href: `/${locale}/games/strategy` },
                      ].map((category) => (
                        <TouchButton
                          key={category.href}
                          onClick={() => setIsMenuOpen(false)}
                          className="w-full text-left"
                        >
                          <Link
                            href={category.href}
                            className="flex items-center justify-between p-3 rounded-lg hover:bg-secondary transition-colors"
                          >
                            <span>{category.name}</span>
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          </Link>
                        </TouchButton>
                      ))}
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-muted-foreground mb-3 uppercase tracking-wide">
                      Quick Actions
                    </h3>
                    <div className="space-y-1">
                      <TouchButton
                        onClick={() => {
                          setIsMenuOpen(false);
                          // Scroll to top
                          window.scrollTo({ top: 0, behavior: 'smooth' });
                        }}
                        className="w-full text-left"
                      >
                        <div className="flex items-center p-3 rounded-lg hover:bg-secondary transition-colors">
                          <span>Back to Top</span>
                        </div>
                      </TouchButton>
                      
                      <TouchButton
                        onClick={() => {
                          setIsMenuOpen(false);
                          // Toggle theme (if you have theme switching)
                        }}
                        className="w-full text-left"
                      >
                        <div className="flex items-center p-3 rounded-lg hover:bg-secondary transition-colors">
                          <span>Toggle Theme</span>
                        </div>
                      </TouchButton>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="p-4 border-t border-border">
                <div className="text-xs text-muted-foreground text-center">
                  FreeHubGames - Mobile Optimized
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Spacer for bottom navigation */}
      <div className="h-16" />
    </>
  );
}

// Swipe navigation for pages
interface SwipeNavigationProps {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  children: React.ReactNode;
  className?: string;
}

export function SwipeNavigation({
  onSwipeLeft,
  onSwipeRight,
  children,
  className = '',
}: SwipeNavigationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useMobileDetection();

  useTouchGestures(containerRef, {
    onSwipe: (gesture) => {
      if (gesture.direction === 'left' && onSwipeLeft) {
        onSwipeLeft();
      } else if (gesture.direction === 'right' && onSwipeRight) {
        onSwipeRight();
      }
    },
    swipeThreshold: 100, // Require longer swipe for navigation
  });

  if (!isMobile) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div ref={containerRef} className={`touch-pan-y ${className}`}>
      {children}
    </div>
  );
}

// Pull-to-refresh component
interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  className?: string;
}

export function PullToRefresh({
  onRefresh,
  children,
  className = '',
}: PullToRefreshProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useMobileDetection();

  useTouchGestures(containerRef, {
    onPan: (gesture) => {
      if (window.scrollY === 0 && gesture.deltaY > 0) {
        const distance = Math.min(gesture.deltaY * 0.5, 100);
        setPullDistance(distance);
      }
    },
  });

  const handleRefresh = async () => {
    if (pullDistance > 60 && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
        setPullDistance(0);
      }
    } else {
      setPullDistance(0);
    }
  };

  if (!isMobile) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div ref={containerRef} className={className}>
      {/* Pull indicator */}
      {pullDistance > 0 && (
        <div
          className="flex items-center justify-center py-4 transition-all duration-200"
          style={{ transform: `translateY(${pullDistance}px)` }}
        >
          <div className={`animate-spin rounded-full h-6 w-6 border-b-2 border-primary ${
            isRefreshing ? 'opacity-100' : 'opacity-50'
          }`} />
        </div>
      )}
      
      <div
        style={{ transform: `translateY(${pullDistance}px)` }}
        onTouchEnd={handleRefresh}
      >
        {children}
      </div>
    </div>
  );
}
